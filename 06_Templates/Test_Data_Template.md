---
template: test_data
version: 1.1
created: {{date:YYYY-MM-DD}}
modified: {{date:YYYY-MM-DD}}
tags: [test-data, template]
---

# {{FEATURE}} Test Data

**Data Type**: Valid | Invalid | Boundary | Security  
**Format**: CSV | JSON | XML | SQL  
**Date Created**: {{date}}  
**Owner**: {{OWNER}}  

## Template Variables
Set these variables when creating a new note from this template:
- `FEATURE`: Feature name
- `OWNER`: Data owner
- `DESCRIPTION`: Field description
- `LONG_DESCRIPTION`: Long text for boundary testing

## 1. Data Schema
```json
{
  "field1": "{{DESCRIPTION}}",
  "field2": "{{DESCRIPTION}}",
  "field3": "{{DESCRIPTION}}"
}
```

## 2. Data Samples
### Valid Data
```csv
id,name,email,status
1,<PERSON>,<EMAIL>,active
2,<PERSON>,<EMAIL>,inactive
```

### Invalid Data
```csv
id,name,email,status
3,Invalid Name,invalid-email,invalid-status
```

### Boundary Data
```csv
id,name,description
4,Boundary User,"{{LONG_DESCRIPTION}}"
```

## 3. Data Generation
```python
# test_data_generator.py
import csv
from faker import Faker

fake = Faker()
with open('test_data.csv', 'w') as f:
    writer = csv.writer(f)
    writer.writerow(['id', 'name', 'email'])
    for i in range(100):
        writer.writerow([i, fake.name(), fake.email()])
```

## 4. Data Masking
```sql
-- Mask PII in test database
UPDATE users
SET email = CONCAT('user', id, '@example.com'),
    phone = CONCAT('555-', SUBSTR(phone, 5));
```

## 5. Data Validation Rules
| Field | Validation Rule | Example Valid | Example Invalid |
|-------|-----------------|---------------|----------------|
| email | regex: ^.+@.+\..+$ | <EMAIL> | invalid-email |
| age | integer 18-120 | 30 | 150 |

## 6. Usage Examples
```java
// Java test data usage
@Test
public void testWithData() {
    for (TestData data : testData) {
        loginPage.enterCredentials(data.username, data.password);
        assertTrue(homePage.isDisplayed());
    }
}
```

## Tags
#test-data #{{FEATURE}} #data-management