---
template: security_test
version: 1.1
created: {{date:YYYY-MM-DD}}
modified: {{date:YYYY-MM-DD}}
tags: [security-test, template]
---

# {{APPLICATION}} Security Assessment

**Type**: Vulnerability Scan | Penetration Test | Code Review  
**Date**: {{date}}  
**Tester**: {{TESTER}}  
**Scope**: {{SCOPE}}  

## Template Variables
Set these variables when creating a new note from this template:
- `APPLICATION`: Application name
- `TESTER`: Tester name
- `SCOPE`: Test scope
- `ADDITIONAL_STANDARD`: Additional security standard
- `TOOL_1`, `TOOL_2`: Security tools
- `PURPOSE`: Tool purpose
- `VERSION`: Tool version
- `VULNERABILITY_NAME`: Vulnerability name
- `URL_OR_FILE`: Location of vulnerability
- `SEVERITY_LEVEL`: Severity level
- `DESCRIPTION`: Vulnerability description
- `HTTP_REQUEST`: Proof of concept request
- `SOLUTION`: Remediation steps
- `CWE_LINK`: Reference link
- `RECOMMENDATION_1`, `REC<PERSON><PERSON><PERSON>ATION_2`: Security recommendations

## 1. Test Methodology
- OWASP Top 10
- {{ADDITIONAL_STANDARD}}

## 2. Tools Used
| Tool | Purpose | Version |
|------|---------|---------|
| {{TOOL_1}} | {{PURPOSE}} | {{VERSION}} |
| {{TOOL_2}} | {{PURPOSE}} | {{VERSION}} |

## 3. Test Cases
| OWASP Category | Test Case | Status | Severity |
|----------------|-----------|--------|----------|
| Injection | SQL Injection | Failed | Critical |
| Broken Authentication | Session Timeout | Passed | Medium |

## 4. Vulnerability Details
### {{VULNERABILITY_NAME}}
**Location**: {{URL_OR_FILE}}  
**Severity**: {{SEVERITY_LEVEL}}  
**Description**: {{DESCRIPTION}}  
**Proof of Concept**:
```http
{{HTTP_REQUEST}}
```
**Remediation**: {{SOLUTION}}  
**References**: {{CWE_LINK}}

## 5. Risk Matrix
```mermaid
graph TD
    A[Critical] -->|SQL Injection| B[{{APPLICATION}}]
    C[High] -->|XSS| B
    D[Medium] -->|CSRF| B
```

## 6. Recommendations
1. {{RECOMMENDATION_1}}
2. {{RECOMMENDATION_2}}

## Tags
#security #owasp #{{APPLICATION}}