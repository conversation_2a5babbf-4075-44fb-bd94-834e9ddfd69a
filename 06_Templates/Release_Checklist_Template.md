---
template: release_checklist
version: 1.1
created: {{date:YYYY-MM-DD}}
modified: {{date:YYYY-MM-DD}}
tags: [release, checklist, template]
---

# {{RELEASE_VERSION}} Release Checklist

**Release Date**: {{date}}  
**Release Manager**: {{RELEASE_MANAGER}}  
**Environment**: Production  

## Template Variables
Set these variables when creating a new note from this template:
- `RELEASE_VERSION`: Release version number (e.g., 1.2.0)
- `RELEASE_MANAGER`: Name of release manager
- `PERFORMANCE_REPORT`: Link to performance report
- `SECURITY_REPORT`: Link to security report
- `DOCUMENTATION_VERSION`: Documentation version
- `PREVIOUS_VERSION`: Previous version for rollback

## 1. Pre-Release Verification
- [ ] All critical bugs resolved (BUG-{{BUG_ID}})
- [ ] Performance testing completed ([[Performance_Reports/{{PERFORMANCE_REPORT}}]])
- [ ] Security scan passed ([[Security_Reports/{{SECURITY_REPORT}}]])
- [ ] Documentation updated ([[Documentation/{{DOCUMENTATION_VERSION}}]])

## 2. Deployment Steps
```mermaid
graph LR
    A[Build Artifact] --> B[Staging]
    B --> C[Smoke Test]
    C --> D[Production]
    D --> E[Verification]
```

### Deployment Commands
```bash
# Build application
mvn clean package -DskipTests

# Deploy to production
ansible-playbook deploy-prod.yml -e "version={{RELEASE_VERSION}}"
```

## 3. Smoke Test Cases
| Test Case | Owner | Status | Notes |
|-----------|-------|--------|-------|
| [[Test_Cases/{{TC_ID}}]] | {{OWNER}} | Pass/Fail | {{NOTES}} |
| Login functionality | {{OWNER}} | Pass/Fail | {{NOTES}} |

## 4. Rollback Procedure
```bash
# Rollback to previous version
ansible-playbook rollback.yml -e "version={{PREVIOUS_VERSION}}"
```

## 5. Post-Release Activities
- [ ] Update release notes ([[Release_Notes/{{RELEASE_VERSION}}]])
- [ ] Notify stakeholders
- [ ] Archive test artifacts ([[Archives/{{RELEASE_VERSION}}]])

## 6. Monitoring
| Metric | Threshold | Current Value | Status |
|--------|-----------|---------------|--------|
| Error Rate | < 0.5% | {{ERROR_RATE}}% | OK/Warning/Critical |
| Response Time | < 2s | {{RESPONSE_TIME}}s | OK/Warning/Critical |

## Tags
#release #{{RELEASE_VERSION}} #checklist