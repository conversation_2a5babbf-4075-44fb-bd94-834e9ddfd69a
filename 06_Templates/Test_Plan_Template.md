---
template: test_plan
version: 1.1
created: {{date:YYYY-MM-DD}}
modified: {{date:YYYY-MM-DD}}
tags: [test-plan, template]
---

# {{PROJECT_NAME}} Test Plan

**Version**: v{{VERSION}}  
**Author**: {{AUTHOR}}  
**Date**: {{date}}  
**Status**: Draft | Approved | Obsolete  

## Template Variables
Set these variables when creating a new note from this template:
- `PROJECT_NAME`: Name of the project
- `VERSION`: Project version
- `AUTHOR`: Author name
- `PROJECT_OVERVIEW`: Brief project overview
- `OBJECTIVE_1`, `OBJECTIVE_2`: Test objectives
- `FEATURE_1`, `FEATURE_2`: In-scope features
- `EXCLUDED_FEATURE_1`, `EXCLUDED_FEATURE_2`: Out-of-scope features
- `TOOLS`: Testing tools
- `ENV`: Testing environment
- `OWNER`: Test owner
- `RISK_1`: Potential risk
- `MITIGATION`: Risk mitigation strategy

## 1. Introduction
{{PROJECT_OVERVIEW}}

## 2. Test Objectives
1. {{OBJECTIVE_1}}
2. {{OBJECTIVE_2}}

## 3. Scope
### In Scope
- {{FEATURE_1}}
- {{FEATURE_2}}

### Out of Scope
- {{EXCLUDED_FEATURE_1}}
- {{EXCLUDED_FEATURE_2}}

## 4. Test Strategy
| Test Type | Tools | Environment | Owner |
|-----------|-------|-------------|-------|
| Functional | {{TOOLS}} | {{ENV}} | {{OWNER}} |
| Performance | {{TOOLS}} | {{ENV}} | {{OWNER}} |
| Security | {{TOOLS}} | {{ENV}} | {{OWNER}} |

## 5. Test Deliverables
- [[Test_Cases/Test_Case_Suite]]
- [[Automation/Test_Scripts]]
- [[Reports/Test_Summary_Report]]

## 6. Schedule
```mermaid
gantt
    title Test Execution Timeline
    dateFormat  YYYY-MM-DD
    section Functional
    Test Design      :a1, {{DATE_START}}, 7d
    Test Execution   :a2, after a1, 14d
    section Performance
    Test Design      :b1, after a1, 5d
    Test Execution   :b2, after b1, 10d
```

## 7. Risks
| Risk | Mitigation | Owner |
|------|------------|-------|
| {{RISK_1}} | {{MITIGATION}} | {{OWNER}} |

## Tags
#test-plan #{{PROJECT_NAME}} #v{{VERSION}}