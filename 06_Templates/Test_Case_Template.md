---
template: test_case
version: 1.1
created: {{date:YYYY-MM-DD}}
modified: {{date:YYYY-MM-DD}}
tags: [test-case, template]
---

# {{TEST_CASE_TITLE}}

**ID**: TC-{{ID}}  
**Module**: {{MODULE}}  
**Priority**: {{PRIORITY}}  
**Status**: Draft | Ready | Deprecated

## Template Variables
Set these variables when creating a new note from this template:
- `TEST_CASE_TITLE`: Descriptive title of the test case
- `ID`: Unique test case identifier
- `MODULE`: Module or component being tested
- `PRIORITY`: Priority level (High/Medium/Low)
- `TEST_CASE_DESCRIPTION`: Brief description of the test case
- `PRECONDITION_1`, `PRECONDITION_2`: Preconditions for the test
- `ACTION_1`, `ACTION_2`: Test step actions
- `RESULT_1`, `RESULT_2`: Expected results
- `TEST_DATA_DESCRIPTION`: Description of test data
- `SCRIPT_NAME`: Automation script name
- `DATA_FILE`: Test data file name

## Description
{{TEST_CASE_DESCRIPTION}}

## Preconditions
1. {{PRECONDITION_1}}
2. {{PRECONDITION_2}}

## Test Steps
| Step | Action | Expected Result |
|------|--------|-----------------|
| 1    | {{ACTION_1}} | {{RESULT_1}} |
| 2    | {{ACTION_2}} | {{RESULT_2}} |

## Test Data
{{TEST_DATA_DESCRIPTION}}

## Associated Files
- [[Automation_Scripts/{{SCRIPT_NAME}}]]
- [[Test_Data/{{DATA_FILE}}]]

## Tags
#test-case #{{MODULE}} #priority-{{PRIORITY}}