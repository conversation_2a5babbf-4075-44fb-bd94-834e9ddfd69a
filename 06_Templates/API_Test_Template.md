---
template: api_test
version: 1.1
created: {{date:YYYY-MM-DD}}
modified: {{date:YYYY-MM-DD}}
tags: [api-test, template]
---

# {{API_NAME}} API Test Report

**Endpoint**: `{{BASE_URL}}`  
**Version**: {{API_VERSION}}  
**Test Date**: {{date}}  
**Environment**: {{ENVIRONMENT}}  

## Template Variables
Set these variables when creating a new note from this template:
- `API_NAME`: API name
- `BASE_URL`: Base endpoint URL
- `API_VERSION`: API version
- `ENVIRONMENT`: Test environment
- `SCENARIO`: Test scenario
- `NOTE`: Additional notes
- `METHOD`: HTTP method
- `ENDPOINT`: API endpoint
- `CONTENT_TYPE`: Content type
- `REQUEST_BODY`: Request body
- `TEST_CASE`: Test case description
- `STEPS`: Reproduction steps
- `TOKEN`: Authentication token
- `RECOMMENDATION_1`, `RECOMMENDATION_2`: Recommendations

## 1. Test Cases
| Scenario | Method | Endpoint | Status | Notes |
|----------|--------|----------|--------|-------|
| {{SCENARIO}} | GET | /resource | Pass | {{NOTE}} |
| {{SCENARIO}} | POST | /resource | Fail | {{NOTE}} |

## 2. Request Examples
### Successful Request
```http
{{METHOD}} {{ENDPOINT}}
Content-Type: {{CONTENT_TYPE}}

{{REQUEST_BODY}}
```

### Response Validation
```javascript
pm.test("Status code is 200", () => {
    pm.response.to.have.status(200);
});

pm.test("Response time < 500ms", () => {
    pm.expect(pm.response.responseTime).to.be.below(500);
});
```

## 3. Error Cases
| Error Code | Test Case | Reproduction Steps |
|------------|-----------|-------------------|
| 400 | {{TEST_CASE}} | {{STEPS}} |
| 401 | {{TEST_CASE}} | {{STEPS}} |
| 500 | {{TEST_CASE}} | {{STEPS}} |

## 4. Performance Metrics
| Endpoint | Avg Response (ms) | 90th % (ms) | Requests/sec | Error Rate |
|----------|-------------------|-------------|--------------|------------|
| /resource | 120 | 250 | 85 | 0.2% |

## 5. Test Data Management
```yaml
test_data:
  valid_token: "Bearer {{TOKEN}}"
  invalid_token: "Bearer expired"
  test_user:
    id: 123
    name: "Test User"
```

## 6. Recommendations
1. {{RECOMMENDATION_1}}
2. {{RECOMMENDATION_2}}

## Tags
#api-test #{{API_NAME}} #rest