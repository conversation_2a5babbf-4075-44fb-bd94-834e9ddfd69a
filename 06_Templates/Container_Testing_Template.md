---
template: container_testing
version: 1.0
created: {{date:YYYY-MM-DD}}
modified: {{date:YYYY-MM-DD}}
tags: [container-testing, docker, kubernetes, template]
---

# {{APPLICATION_NAME}} Container Testing Strategy

**Container Runtime**: {{CONTAINER_RUNTIME}}  
**Orchestration**: {{ORCHESTRATION_PLATFORM}}  
**Base Image**: {{BASE_IMAGE}}  
**Application Version**: {{APP_VERSION}}  
**Testing Environment**: {{TEST_ENVIRONMENT}}  

## Template Variables
Set these variables when creating a new note from this template:
- `APPLICATION_NAME`: Name of the containerized application
- `CONTAINER_RUNTIME`: Docker, <PERSON><PERSON>, containerd
- `ORCHESTRATION_PLATFORM`: Kubernetes, Docker Swarm, OpenShift
- `BASE_IMAGE`: Base container image (e.g., alpine:3.18, ubuntu:22.04)
- `APP_VERSION`: Application version being tested
- `TEST_ENVIRONMENT`: Testing environment (dev, staging, prod)
- `DOCKERFILE_PATH`: Path to Dockerfile
- `COMPOSE_FILE`: Docker Compose file name
- `K8S_MANIFEST`: Kubernetes manifest file
- `TEST_COMMAND`: Command to run tests inside container

## Container Testing Scope

### 1. Image Testing
- **Security Scanning** - Vulnerability assessment
- **Size Optimization** - Image layer analysis
- **Compliance** - Policy and standard adherence
- **Dependency Analysis** - Package and library audit

### 2. Runtime Testing
- **Functionality** - Application behavior validation
- **Performance** - Resource utilization testing
- **Networking** - Service communication testing
- **Storage** - Volume and persistence testing

### 3. Orchestration Testing
- **Deployment** - Rolling updates and rollbacks
- **Scaling** - Horizontal and vertical scaling
- **Service Discovery** - Inter-service communication
- **Health Checks** - Liveness and readiness probes

## Test Implementation

### Dockerfile Testing
```dockerfile
# Multi-stage build for testing
FROM {{BASE_IMAGE}} AS test
COPY . /app
WORKDIR /app
RUN {{TEST_COMMAND}}

FROM {{BASE_IMAGE}} AS production
COPY --from=test /app/dist /app
EXPOSE 8080
CMD ["./start.sh"]
```

### Docker Compose Test Setup
```yaml
# {{COMPOSE_FILE}}
version: '3.8'
services:
  app:
    build: .
    ports:
      - "8080:8080"
    environment:
      - NODE_ENV=test
    depends_on:
      - database
      - redis
    
  database:
    image: postgres:15
    environment:
      POSTGRES_DB: testdb
      POSTGRES_USER: testuser
      POSTGRES_PASSWORD: testpass
    
  redis:
    image: redis:7-alpine
```

### Kubernetes Testing Manifests
```yaml
# {{K8S_MANIFEST}}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{APPLICATION_NAME}}-test
spec:
  replicas: 1
  selector:
    matchLabels:
      app: {{APPLICATION_NAME}}
  template:
    metadata:
      labels:
        app: {{APPLICATION_NAME}}
    spec:
      containers:
      - name: app
        image: {{APPLICATION_NAME}}:{{APP_VERSION}}
        ports:
        - containerPort: 8080
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 5
```

## Security Testing

### Image Vulnerability Scanning
```bash
# Trivy security scan
trivy image {{APPLICATION_NAME}}:{{APP_VERSION}}

# Snyk container scan
snyk container test {{APPLICATION_NAME}}:{{APP_VERSION}}

# Docker Scout (if available)
docker scout cves {{APPLICATION_NAME}}:{{APP_VERSION}}
```

### Runtime Security Testing
```bash
# Container runtime security
docker run --rm -it \
  -v /var/run/docker.sock:/var/run/docker.sock \
  aquasec/trivy image {{APPLICATION_NAME}}:{{APP_VERSION}}

# Kubernetes security scanning
kubectl apply -f https://raw.githubusercontent.com/aquasecurity/kube-bench/main/job.yaml
```

## Performance Testing

### Resource Monitoring
```bash
# Container resource usage
docker stats {{APPLICATION_NAME}}

# Kubernetes resource monitoring
kubectl top pods -l app={{APPLICATION_NAME}}
kubectl describe pod {{APPLICATION_NAME}}-pod
```

### Load Testing
```yaml
# K6 load test for containerized app
import http from 'k6/http';
import { check } from 'k6';

export let options = {
  stages: [
    { duration: '2m', target: 10 },
    { duration: '5m', target: 50 },
    { duration: '2m', target: 0 },
  ],
};

export default function() {
  let response = http.get('http://{{APPLICATION_NAME}}:8080/api/health');
  check(response, {
    'status is 200': (r) => r.status === 200,
    'response time < 500ms': (r) => r.timings.duration < 500,
  });
}
```

## Test Automation Scripts

### Container Build & Test Pipeline
```bash
#!/bin/bash
# build-and-test.sh

set -e

echo "Building container image..."
docker build -t {{APPLICATION_NAME}}:{{APP_VERSION}} {{DOCKERFILE_PATH}}

echo "Running security scan..."
trivy image {{APPLICATION_NAME}}:{{APP_VERSION}}

echo "Starting test environment..."
docker-compose -f {{COMPOSE_FILE}} up -d

echo "Running integration tests..."
docker-compose -f {{COMPOSE_FILE}} exec app npm test

echo "Running performance tests..."
k6 run performance-test.js

echo "Cleaning up..."
docker-compose -f {{COMPOSE_FILE}} down
```

### Kubernetes Testing Script
```bash
#!/bin/bash
# k8s-test.sh

set -e

echo "Applying Kubernetes manifests..."
kubectl apply -f {{K8S_MANIFEST}}

echo "Waiting for deployment to be ready..."
kubectl wait --for=condition=available --timeout=300s deployment/{{APPLICATION_NAME}}-test

echo "Running health checks..."
kubectl get pods -l app={{APPLICATION_NAME}}

echo "Running port-forward for testing..."
kubectl port-forward service/{{APPLICATION_NAME}} 8080:8080 &
PF_PID=$!

sleep 5

echo "Running API tests..."
npm run test:api

echo "Cleaning up..."
kill $PF_PID
kubectl delete -f {{K8S_MANIFEST}}
```

## Test Results & Reporting

### Container Metrics
- **Build Time**: Image build duration
- **Image Size**: Final image size and layers
- **Security Score**: Vulnerability scan results
- **Resource Usage**: CPU and memory consumption

### Test Coverage
- **Unit Tests**: Code coverage within container
- **Integration Tests**: Service interaction validation
- **End-to-End Tests**: Complete workflow testing
- **Performance Tests**: Load and stress testing results

## Related Files
- [[{{DOCKERFILE_PATH}}]]
- [[{{COMPOSE_FILE}}]]
- [[{{K8S_MANIFEST}}]]
- [[Security_Scans/{{APPLICATION_NAME}}_vulnerability_report]]

## Tags
#container-testing #docker #kubernetes #devops #{{APPLICATION_NAME}}
