---
template: performance_test
version: 1.1
created: {{date:YYYY-MM-DD}}
modified: {{date:YYYY-MM-DD}}
tags: [performance-test, template]
---

# {{SYSTEM_UNDER_TEST}} Performance Test

**Test Type**: Load | Stress | Endurance | Spike  
**Test Environment**: {{ENVIRONMENT}}  
**Test Date**: {{date}}  
**Version**: {{VERSION}}  

## Template Variables
Set these variables when creating a new note from this template:
- `SYSTEM_UNDER_TEST`: System being tested
- `ENVIRONMENT`: Test environment
- `VERSION`: Software version
- `PERFORMANCE_GOALS`: Performance goals/objectives
- `SCENARIO_NAME`: Test scenario name
- `USERS`: Number of virtual users
- `MINUTES`: Duration in minutes
- `DESCRIPTION`: Scenario description
- `DATA_PARAMETER`, `DATA_VALUE`: Test data parameters
- `TOOL`: Monitoring tool
- `SECONDS`: Sampling interval
- `LANGUAGE`: Scripting language
- `PERFORMANCE_TEST_SCRIPT`: Performance test script
- `RECOMMENDATION_1`, `REC<PERSON><PERSON>NDATION_2`: Performance recommendations

## 1. Objectives
{{PERFORMANCE_GOALS}}

## 2. Test Scenarios
| Scenario | Virtual Users | Duration | Ramp-up | Description |
|----------|---------------|----------|---------|-------------|
| {{SCENARIO_NAME}} | {{USERS}} | {{MINUTES}}m | {{MINUTES}}m | {{DESCRIPTION}} |

## 3. Test Data
```csv
{{DATA_PARAMETER}},{{DATA_VALUE}}
username1,password1
username2,password2
```

## 4. Metrics Collection
| Metric | Tool | Sampling Interval |
|--------|------|-------------------|
| Response Time | {{TOOL}} | {{SECONDS}}s |
| Throughput | {{TOOL}} | {{SECONDS}}s |
| Error Rate | {{TOOL}} | {{SECONDS}}s |
| Resource Utilization | {{TOOL}} | {{SECONDS}}s |

## 5. Test Script
```{{LANGUAGE}}
{{PERFORMANCE_TEST_SCRIPT}}
```

## 6. Results Summary
```mermaid
graph LR
    A[Response Time] --> B[95% < 2s]
    C[Throughput] --> D[100 req/s]
    E[Error Rate] --> F[< 0.5%]
```

## 7. Detailed Results
| Transaction | Samples | Average (ms) | Min (ms) | Max (ms) | Error % |
|-------------|---------|--------------|----------|----------|---------|
| Login | 10,000 | 1200 | 800 | 3500 | 0.2 |
| Search | 15,000 | 800 | 400 | 2500 | 0.1 |

## 8. Recommendations
1. {{RECOMMENDATION_1}}
2. {{RECOMMENDATION_2}}

## Tags
#performance #load-test #{{SYSTEM_UNDER_TEST}}