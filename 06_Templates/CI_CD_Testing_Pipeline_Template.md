---
template: ci_cd_testing_pipeline
version: 1.0
created: {{date:YYYY-MM-DD}}
modified: {{date:YYYY-MM-DD}}
tags: [ci-cd, pipeline, automation, testing]
---

# {{PROJECT_NAME}} CI/CD Testing Pipeline

**Platform**: {{CI_PLATFORM}}  
**Repository**: {{REPOSITORY_URL}}  
**Branch Strategy**: {{BRANCH_STRATEGY}}  
**Deployment Target**: {{DEPLOYMENT_TARGET}}  
**Testing Framework**: {{TESTING_FRAMEWORK}}  

## Template Variables
Set these variables when creating a new note from this template:
- `PROJECT_NAME`: Name of the project
- `CI_PLATFORM`: CI/CD platform (GitHub Actions, GitLab CI, Jenkins, etc.)
- `REPOSITORY_URL`: Git repository URL
- `BRANCH_STRATEGY`: Branching strategy (GitFlow, GitHub Flow, etc.)
- `DEPLOYMENT_TARGET`: Deployment environment (AWS, Azure, GCP, etc.)
- `TESTING_FRAMEWORK`: Primary testing framework
- `LANGUAGE`: Programming language
- `BUILD_TOOL`: Build tool (Maven, Gradle, npm, etc.)
- `DOCKER_IMAGE`: Base Docker image for testing
- `TEST_COMMAND`: Command to run tests

## Pipeline Overview

```mermaid
graph TD
    A[Code Push] --> B[Trigger Pipeline]
    B --> C[Code Quality Check]
    C --> D[Unit Tests]
    D --> E[Build Application]
    E --> F[Integration Tests]
    F --> G[Security Scan]
    G --> H[Performance Tests]
    H --> I[Deploy to Staging]
    I --> J[E2E Tests]
    J --> K[Deploy to Production]
    
    C --> L[SonarQube]
    D --> M[Coverage Report]
    F --> N[Test Reports]
    G --> O[Security Report]
    H --> P[Performance Report]
    J --> Q[E2E Report]
```

## GitHub Actions Configuration

### Main Workflow (.github/workflows/ci.yml)
```yaml
name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  NODE_VERSION: '18'
  PYTHON_VERSION: '3.11'
  JAVA_VERSION: '17'

jobs:
  code-quality:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup {{LANGUAGE}}
        uses: actions/setup-{{LANGUAGE}}@v4
        with:
          {{LANGUAGE}}-version: ${{ env.{{LANGUAGE}}_VERSION }}
          
      - name: Install dependencies
        run: {{BUILD_TOOL}} install
        
      - name: Lint code
        run: {{BUILD_TOOL}} run lint
        
      - name: Format check
        run: {{BUILD_TOOL}} run format:check
        
      - name: Security audit
        run: {{BUILD_TOOL}} audit

  unit-tests:
    runs-on: ubuntu-latest
    needs: code-quality
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup {{LANGUAGE}}
        uses: actions/setup-{{LANGUAGE}}@v4
        with:
          {{LANGUAGE}}-version: ${{ env.{{LANGUAGE}}_VERSION }}
          
      - name: Install dependencies
        run: {{BUILD_TOOL}} install
        
      - name: Run unit tests
        run: {{TEST_COMMAND}}
        
      - name: Generate coverage report
        run: {{BUILD_TOOL}} run coverage
        
      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage/lcov.info

  build:
    runs-on: ubuntu-latest
    needs: unit-tests
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup {{LANGUAGE}}
        uses: actions/setup-{{LANGUAGE}}@v4
        with:
          {{LANGUAGE}}-version: ${{ env.{{LANGUAGE}}_VERSION }}
          
      - name: Install dependencies
        run: {{BUILD_TOOL}} install
        
      - name: Build application
        run: {{BUILD_TOOL}} run build
        
      - name: Build Docker image
        run: |
          docker build -t {{PROJECT_NAME}}:${{ github.sha }} .
          docker tag {{PROJECT_NAME}}:${{ github.sha }} {{PROJECT_NAME}}:latest
          
      - name: Save Docker image
        run: docker save {{PROJECT_NAME}}:latest | gzip > {{PROJECT_NAME}}.tar.gz
        
      - name: Upload build artifact
        uses: actions/upload-artifact@v3
        with:
          name: docker-image
          path: {{PROJECT_NAME}}.tar.gz

  integration-tests:
    runs-on: ubuntu-latest
    needs: build
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: testdb
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    steps:
      - uses: actions/checkout@v4
      
      - name: Download build artifact
        uses: actions/download-artifact@v3
        with:
          name: docker-image
          
      - name: Load Docker image
        run: docker load < {{PROJECT_NAME}}.tar.gz
        
      - name: Run integration tests
        run: |
          docker-compose -f docker-compose.test.yml up -d
          {{BUILD_TOOL}} run test:integration
          docker-compose -f docker-compose.test.yml down

  security-scan:
    runs-on: ubuntu-latest
    needs: build
    steps:
      - uses: actions/checkout@v4
      
      - name: Download build artifact
        uses: actions/download-artifact@v3
        with:
          name: docker-image
          
      - name: Load Docker image
        run: docker load < {{PROJECT_NAME}}.tar.gz
        
      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: '{{PROJECT_NAME}}:latest'
          format: 'sarif'
          output: 'trivy-results.sarif'
          
      - name: Upload Trivy scan results
        uses: github/codeql-action/upload-sarif@v2
        with:
          sarif_file: 'trivy-results.sarif'

  performance-tests:
    runs-on: ubuntu-latest
    needs: build
    if: github.ref == 'refs/heads/main'
    steps:
      - uses: actions/checkout@v4
      
      - name: Download build artifact
        uses: actions/download-artifact@v3
        with:
          name: docker-image
          
      - name: Load Docker image
        run: docker load < {{PROJECT_NAME}}.tar.gz
        
      - name: Start application
        run: |
          docker run -d -p 8080:8080 --name app {{PROJECT_NAME}}:latest
          sleep 30
          
      - name: Run performance tests
        run: |
          npx k6 run performance-tests/load-test.js
          
      - name: Upload performance results
        uses: actions/upload-artifact@v3
        with:
          name: performance-results
          path: performance-results/

  deploy-staging:
    runs-on: ubuntu-latest
    needs: [integration-tests, security-scan]
    if: github.ref == 'refs/heads/develop'
    environment: staging
    steps:
      - uses: actions/checkout@v4
      
      - name: Deploy to staging
        run: |
          # Add deployment commands here
          echo "Deploying to staging environment"
          
      - name: Run smoke tests
        run: {{BUILD_TOOL}} run test:smoke

  e2e-tests:
    runs-on: ubuntu-latest
    needs: deploy-staging
    if: github.ref == 'refs/heads/develop'
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          
      - name: Install Playwright
        run: |
          npm install @playwright/test
          npx playwright install
          
      - name: Run E2E tests
        run: npx playwright test
        env:
          BASE_URL: https://staging.{{PROJECT_NAME}}.com
          
      - name: Upload E2E results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: playwright-report
          path: playwright-report/

  deploy-production:
    runs-on: ubuntu-latest
    needs: [e2e-tests, performance-tests]
    if: github.ref == 'refs/heads/main'
    environment: production
    steps:
      - uses: actions/checkout@v4
      
      - name: Deploy to production
        run: |
          # Add production deployment commands
          echo "Deploying to production environment"
          
      - name: Run production smoke tests
        run: {{BUILD_TOOL}} run test:smoke:production
```

## GitLab CI Configuration

### .gitlab-ci.yml
```yaml
stages:
  - quality
  - test
  - build
  - security
  - performance
  - deploy-staging
  - e2e
  - deploy-production

variables:
  DOCKER_DRIVER: overlay2
  DOCKER_TLS_CERTDIR: "/certs"

before_script:
  - echo "Starting CI/CD pipeline for {{PROJECT_NAME}}"

code-quality:
  stage: quality
  image: {{DOCKER_IMAGE}}
  script:
    - {{BUILD_TOOL}} install
    - {{BUILD_TOOL}} run lint
    - {{BUILD_TOOL}} run format:check
  artifacts:
    reports:
      codequality: code-quality-report.json

unit-tests:
  stage: test
  image: {{DOCKER_IMAGE}}
  script:
    - {{BUILD_TOOL}} install
    - {{TEST_COMMAND}}
  coverage: '/Coverage: \d+\.\d+%/'
  artifacts:
    reports:
      coverage_report:
        coverage_format: cobertura
        path: coverage/cobertura-coverage.xml
      junit: test-results.xml

build-image:
  stage: build
  image: docker:latest
  services:
    - docker:dind
  script:
    - docker build -t $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA .
    - docker push $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA

security-scan:
  stage: security
  image: docker:latest
  services:
    - docker:dind
  script:
    - docker run --rm -v /var/run/docker.sock:/var/run/docker.sock 
      aquasec/trivy image $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA
  artifacts:
    reports:
      container_scanning: security-report.json

deploy-staging:
  stage: deploy-staging
  image: alpine:latest
  script:
    - echo "Deploying to staging"
    # Add staging deployment commands
  environment:
    name: staging
    url: https://staging.{{PROJECT_NAME}}.com
  only:
    - develop

e2e-tests:
  stage: e2e
  image: mcr.microsoft.com/playwright:latest
  script:
    - npm install
    - npx playwright test
  artifacts:
    when: always
    paths:
      - playwright-report/
    expire_in: 30 days
  only:
    - develop

deploy-production:
  stage: deploy-production
  image: alpine:latest
  script:
    - echo "Deploying to production"
    # Add production deployment commands
  environment:
    name: production
    url: https://{{PROJECT_NAME}}.com
  when: manual
  only:
    - main
```

## Jenkins Pipeline Configuration

### Jenkinsfile
```groovy
pipeline {
    agent any
    
    environment {
        DOCKER_IMAGE = "{{PROJECT_NAME}}"
        REGISTRY = "your-registry.com"
    }
    
    stages {
        stage('Checkout') {
            steps {
                checkout scm
            }
        }
        
        stage('Code Quality') {
            parallel {
                stage('Lint') {
                    steps {
                        sh '{{BUILD_TOOL}} run lint'
                    }
                }
                stage('Security Audit') {
                    steps {
                        sh '{{BUILD_TOOL}} audit'
                    }
                }
            }
        }
        
        stage('Test') {
            parallel {
                stage('Unit Tests') {
                    steps {
                        sh '{{TEST_COMMAND}}'
                    }
                    post {
                        always {
                            publishTestResults testResultsPattern: 'test-results.xml'
                            publishCoverage adapters: [
                                coberturaAdapter('coverage/cobertura-coverage.xml')
                            ]
                        }
                    }
                }
                stage('Integration Tests') {
                    steps {
                        sh 'docker-compose -f docker-compose.test.yml up -d'
                        sh '{{BUILD_TOOL}} run test:integration'
                        sh 'docker-compose -f docker-compose.test.yml down'
                    }
                }
            }
        }
        
        stage('Build') {
            steps {
                sh 'docker build -t ${DOCKER_IMAGE}:${BUILD_NUMBER} .'
                sh 'docker tag ${DOCKER_IMAGE}:${BUILD_NUMBER} ${DOCKER_IMAGE}:latest'
            }
        }
        
        stage('Security Scan') {
            steps {
                sh 'trivy image ${DOCKER_IMAGE}:${BUILD_NUMBER}'
            }
        }
        
        stage('Deploy to Staging') {
            when {
                branch 'develop'
            }
            steps {
                sh 'echo "Deploying to staging"'
                // Add staging deployment commands
            }
        }
        
        stage('E2E Tests') {
            when {
                branch 'develop'
            }
            steps {
                sh 'npx playwright test'
            }
            post {
                always {
                    publishHTML([
                        allowMissing: false,
                        alwaysLinkToLastBuild: true,
                        keepAll: true,
                        reportDir: 'playwright-report',
                        reportFiles: 'index.html',
                        reportName: 'Playwright Report'
                    ])
                }
            }
        }
        
        stage('Deploy to Production') {
            when {
                branch 'main'
            }
            steps {
                input message: 'Deploy to production?', ok: 'Deploy'
                sh 'echo "Deploying to production"'
                // Add production deployment commands
            }
        }
    }
    
    post {
        always {
            cleanWs()
        }
        failure {
            emailext (
                subject: "Pipeline Failed: ${env.JOB_NAME} - ${env.BUILD_NUMBER}",
                body: "Pipeline failed. Check console output at ${env.BUILD_URL}",
                to: "team@{{PROJECT_NAME}}.com"
            )
        }
    }
}
```

## Testing Strategy Integration

### Test Types by Stage
- **Code Quality**: Linting, formatting, security audit
- **Unit Tests**: Component-level testing with coverage
- **Integration Tests**: Service interaction testing
- **Security Tests**: Vulnerability scanning and SAST
- **Performance Tests**: Load and stress testing
- **E2E Tests**: Complete user journey validation
- **Smoke Tests**: Basic functionality verification

### Quality Gates
- **Code Coverage**: Minimum 80% coverage required
- **Security**: No high/critical vulnerabilities
- **Performance**: Response time < 500ms for 95th percentile
- **E2E**: All critical user journeys pass

## Related Files
- [[CI_CD_Configs/{{CI_PLATFORM}}_config]]
- [[Test_Scripts/{{TESTING_FRAMEWORK}}_suite]]
- [[Deployment_Scripts/{{DEPLOYMENT_TARGET}}_deploy]]

## Tags
#ci-cd #pipeline #automation #{{CI_PLATFORM}} #{{PROJECT_NAME}}
