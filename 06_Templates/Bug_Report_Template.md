---
template: bug_report
version: 1.1
created: {{date:YYYY-MM-DD}}
modified: {{date:YYYY-MM-DD}}
tags: [bug-report, template]
---

# {{BUG_TITLE}} (BUG-{{ID}})

**Severity**: Critical | High | Medium | Low  
**Priority**: P0 | P1 | P2 | P3  
**Status**: New | In Progress | Resolved | Reopened | Closed  
**Environment**: {{ENVIRONMENT}} ({{VERSION}})  
**Reporter**: {{REPORTER}}  
**Date**: {{date}}

## Template Variables
Set these variables when creating a new note from this template:
- `BUG_TITLE`: Descriptive title of the bug
- `ID`: Unique bug identifier
- `ENVIRONMENT`: Environment where bug was found (e.g., Production, Staging)
- `VERSION`: Software version
- `REPORTER`: Name of reporter
- `BUG_DESCRIPTION`: Detailed description of the bug
- `STEP_1`, `STEP_2`, `STEP_3`: Steps to reproduce
- `EXPECTED_BEHAVIOR`: Expected behavior
- `ACTUAL_BEHAVIOR`: Actual behavior
- `SCREENSHOT`: Screenshot filename
- `LOG_EXCERPT`: Relevant log excerpt
- `MODULE_OR_FILE`: Affected module or file
- `RELATED_COMPONENT`: Related component
- `TEMPORARY_WORKAROUND`: Temporary workaround
- `ROOT_CAUSE`: Root cause analysis

## Description
{{BUG_DESCRIPTION}}

## Steps to Reproduce
1. {{STEP_1}}
2. {{STEP_2}}
3. {{STEP_3}}

## Expected Result
{{EXPECTED_BEHAVIOR}}

## Actual Result
{{ACTUAL_BEHAVIOR}}

## Evidence
- Screenshot: ![[Attachments/{{SCREENSHOT}}]]
- Logs: 
```text
{{LOG_EXCERPT}}
```

## Affected Components
- [[{{MODULE_OR_FILE}}]]
- [[{{RELATED_COMPONENT}}]]

## Workaround
{{TEMPORARY_WORKAROUND}}

## Root Cause Analysis
{{ROOT_CAUSE}}

## Tags
#bug #{{MODULE}} #{{SEVERITY}}-priority