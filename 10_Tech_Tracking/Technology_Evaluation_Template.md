# ${TECHNOLOGY_NAME} Evaluation

**Category**: ${CATEGORY}  
**Version**: ${VERSION}  
**Evaluation Date**: ${DATE}  
**Evaluator**: ${YOUR_NAME}  
**Status**: Assessing | Recommended | Rejected  

## Overview
${TECH_DESCRIPTION}

[Official Documentation](${DOCS_URL})
[GitHub Repository](${REPO_URL})

## Evaluation Criteria
| Criteria | Rating (1-5) | Notes |
|----------|--------------|-------|
| **Learning Curve** | ${RATING} | ${NOTES} |
| **Integration Effort** | ${RATING} | ${NOTES} |
| **Team Skill Match** | ${RATING} | ${NOTES} |
| **Performance** | ${RATING} | ${NOTES} |
| **Community Support** | ${RATING} | ${NOTES} |
| **ROI Potential** | ${RATING} | ${NOTES} |

## Proof of Concept
```${LANGUAGE}
${POC_CODE_SNIPPET}
```

**POC Results**:
${POC_RESULTS}

## Comparison with Alternatives
| Feature | ${TECHNOLOGY_NAME} | ${ALTERNATIVE} |
|---------|--------------------|----------------|
| ${FEATURE_1} | ${RATING} | ${RATING} |
| ${FEATURE_2} | ${RATING} | ${RATING} |

## Adoption Recommendations
1. ${RECOMMENDATION_1}
2. ${RECOMMENDATION_2}

## Technology Radar Position
```mermaid
graph TD
    A[Adopt] -->|${TECHNOLOGY_NAME}| B(${CATEGORY})
    C[Trial] -->|${RELATED_TECH}| B
    D[Assess] -->|${NEW_TECH}| B
```

## Tags
#tech-eval #${CATEGORY} #${STATUS}