# Obsidian Test Engineering Dashboard

```mermaid
graph TD
    A[Workflow] --> B[Projects]
    A --> C[Areas]
    A --> D[Reviews]
    B --> E[Environments]
    B --> F[Testing Assets]
    G[Automation] --> H[Frameworks]
    G --> I[CI/CD]
    J[Knowledge] --> K[Tools]
    J --> L[Standards]
```

## Quick Access
| Section | Description | Link |
|---------|-------------|------|
| **Active Projects** | Current testing initiatives | [[01_Projects]] |
| **Test Design** | Templates and cases | [[06_Templates]] |
| **Automation** | Scripts and frameworks | [[04_Automation_Framework]] |
| **Tech Tracking** | Emerging tools/technologies | [[10_Tech_Tracking]] |
| **Command Reference** | Quick lookup | [[13_Quick_Reference]] |

## Recent Notes
```dataview
TABLE file.mtime AS "Last Modified"
FROM ""
SORT file.mtime DESC
LIMIT 5
```

## Vault Statistics
```dataview
TABLE length(file.inlinks) AS Inlinks, length(file.outlinks) AS Outlinks
FROM ""
SORT file.name
```

## Quick Actions
- [[New Test Case|06_Templates/Test_Case_Template]]
- [[Report Bug|06_Templates/Bug_Report_Template]]
- [[Evaluate Technology|10_Tech_Tracking/Technology_Evaluation_Template]]