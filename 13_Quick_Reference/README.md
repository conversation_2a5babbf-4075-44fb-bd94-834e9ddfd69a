# Command & Snippet Quick Reference

```mermaid
graph LR
    A[Linux] --> B[File Management]
    A --> C[Process Control]
    A --> D[Networking]
    E[JMeter] --> F[CLI Commands]
    E --> G[JMX Reference]
    H[Snippets] --> I[Java]
    H --> J[Python]
    H --> <PERSON>[Rust]
```

## Browse References
- [[Linux Commands|13_Quick_Reference/Linux]]
- [[JMeter Reference|13_Quick_Reference/JMeter]]
- [[Code Snippets|13_Quick_Reference/Snippets]]

## Search Tips
```dataview
TABLE category, description
FROM "13_Quick_Reference"
SORT file.name
```

## Contribution Guide
1. Use consistent header format
2. Include executable examples
3. Add related links
4. Tag appropriately

> Use `!terminal command` to mark executable commands