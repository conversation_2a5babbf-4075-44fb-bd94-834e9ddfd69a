# Testing Quick Reference Cheat Sheet

## 🧪 Test Types & Levels

### By Test Level
- **Unit Testing** - Individual components/functions
- **Integration Testing** - Component interactions
- **System Testing** - Complete system validation
- **Acceptance Testing** - Business requirements validation

### By Test Type
- **Functional Testing** - Feature behavior validation
- **Non-Functional Testing** - Performance, security, usability
- **Regression Testing** - Existing functionality validation
- **Smoke Testing** - Basic functionality verification

### By Approach
- **Black Box Testing** - External behavior focus
- **White Box Testing** - Internal structure focus
- **Gray Box Testing** - Combination approach

## 🎯 Test Design Techniques

### Equivalence Partitioning
```
Valid Partitions: [1-100]
Invalid Partitions: [<1], [>100]
Test Cases: 50, 0, 101
```

### Boundary Value Analysis
```
Range: 1-100
Test Values: 0, 1, 2, 99, 100, 101
```

### Decision Table Testing
| Condition 1 | Condition 2 | Action 1 | Action 2 |
|-------------|-------------|----------|----------|
| T | T | X | - |
| T | F | - | X |
| F | T | - | X |
| F | F | - | - |

### State Transition Testing
```mermaid
stateDiagram-v2
    [*] --> Idle
    Idle --> Active: start()
    Active --> Paused: pause()
    Paused --> Active: resume()
    Active --> Idle: stop()
    Paused --> Idle: stop()
```

## 🐛 Bug Severity & Priority

### Severity Levels
- **Critical** - System crash, data loss, security breach
- **High** - Major functionality broken, workaround exists
- **Medium** - Minor functionality issues, cosmetic problems
- **Low** - Enhancement requests, documentation issues

### Priority Levels
- **P0** - Fix immediately (production down)
- **P1** - Fix within 24 hours
- **P2** - Fix in current sprint
- **P3** - Fix when time permits

### Bug Lifecycle
```mermaid
graph LR
    A[New] --> B[Assigned]
    B --> C[In Progress]
    C --> D[Fixed]
    D --> E[Verified]
    E --> F[Closed]
    D --> G[Reopened]
    G --> C
```

## 🔧 Testing Tools Quick Commands

### Selenium WebDriver (Python)
```python
# Basic setup
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

driver = webdriver.Chrome()
driver.get("https://example.com")

# Find elements
element = driver.find_element(By.ID, "element-id")
elements = driver.find_elements(By.CLASS_NAME, "class-name")

# Wait for element
wait = WebDriverWait(driver, 10)
element = wait.until(EC.presence_of_element_located((By.ID, "element-id")))

# Actions
element.click()
element.send_keys("text")
element.clear()

# Cleanup
driver.quit()
```

### Playwright (JavaScript)
```javascript
// Basic setup
const { chromium } = require('playwright');

(async () => {
  const browser = await chromium.launch();
  const page = await browser.newPage();
  await page.goto('https://example.com');
  
  // Interactions
  await page.click('#button-id');
  await page.fill('#input-id', 'text');
  await page.selectOption('#select-id', 'option-value');
  
  // Assertions
  await expect(page.locator('#element')).toBeVisible();
  await expect(page.locator('#element')).toHaveText('Expected text');
  
  await browser.close();
})();
```

### REST API Testing (curl)
```bash
# GET request
curl -X GET "https://api.example.com/users" \
  -H "Authorization: Bearer token" \
  -H "Content-Type: application/json"

# POST request
curl -X POST "https://api.example.com/users" \
  -H "Content-Type: application/json" \
  -d '{"name": "John", "email": "<EMAIL>"}'

# PUT request
curl -X PUT "https://api.example.com/users/123" \
  -H "Content-Type: application/json" \
  -d '{"name": "John Updated"}'

# DELETE request
curl -X DELETE "https://api.example.com/users/123" \
  -H "Authorization: Bearer token"
```

### JMeter CLI
```bash
# Run test plan
jmeter -n -t test-plan.jmx -l results.jtl

# Generate HTML report
jmeter -g results.jtl -o html-report/

# Run with properties
jmeter -n -t test-plan.jmx -Jusers=100 -Jrampup=60
```

## 📊 Test Metrics Formulas

### Coverage Metrics
```
Test Coverage = (Tests Executed / Total Tests) × 100
Code Coverage = (Lines Covered / Total Lines) × 100
Requirement Coverage = (Requirements Tested / Total Requirements) × 100
```

### Quality Metrics
```
Defect Density = Total Defects / Size (KLOC)
Defect Removal Efficiency = (Defects Found in Testing / Total Defects) × 100
Test Effectiveness = (Defects Found in Testing / Total Test Cases) × 100
```

### Performance Metrics
```
Response Time = Time to receive complete response
Throughput = Number of requests processed per unit time
Error Rate = (Failed Requests / Total Requests) × 100
```

## 🚀 CI/CD Testing Commands

### Docker Testing
```bash
# Build test image
docker build -t app:test .

# Run tests in container
docker run --rm app:test npm test

# Run with volume mount
docker run --rm -v $(pwd):/app app:test npm test

# Docker Compose testing
docker-compose -f docker-compose.test.yml up --abort-on-container-exit
```

### GitHub Actions
```yaml
# Basic test job
- name: Run Tests
  run: |
    npm install
    npm test
    npm run test:coverage

# Matrix testing
strategy:
  matrix:
    node-version: [16, 18, 20]
    os: [ubuntu-latest, windows-latest, macos-latest]
```

## 🔍 Debugging Tips

### Browser DevTools
- **F12** - Open DevTools
- **Ctrl+Shift+C** - Inspect element
- **Ctrl+Shift+I** - Open DevTools
- **Ctrl+Shift+J** - Open Console

### Common Selenium Issues
```python
# Handle stale element
try:
    element.click()
except StaleElementReferenceException:
    element = driver.find_element(By.ID, "element-id")
    element.click()

# Wait for element to be clickable
wait.until(EC.element_to_be_clickable((By.ID, "button-id")))

# Handle alerts
alert = driver.switch_to.alert
alert.accept()  # or alert.dismiss()
```

### API Testing Debugging
```bash
# Verbose curl output
curl -v -X GET "https://api.example.com/endpoint"

# Save response headers
curl -D headers.txt "https://api.example.com/endpoint"

# Follow redirects
curl -L "https://api.example.com/endpoint"
```

## 📝 Test Documentation Templates

### Test Case Structure
```
ID: TC_001
Title: User Login with Valid Credentials
Preconditions: User account exists
Steps:
1. Navigate to login page
2. Enter valid username
3. Enter valid password
4. Click login button
Expected Result: User is logged in successfully
```

### Bug Report Structure
```
Title: Login button not responding on mobile
Environment: iOS Safari 15.0
Steps to Reproduce:
1. Open app on mobile device
2. Navigate to login page
3. Tap login button
Expected: Login form should submit
Actual: Button does not respond to tap
Severity: High
Priority: P1
```

## 🏷️ Tags
#quick-reference #testing #cheat-sheet #commands #formulas
