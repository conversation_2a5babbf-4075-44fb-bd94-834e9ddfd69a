# Linux File Management Commands

```mermaid
graph TB
    A[File Management] --> B[Viewing]
    A --> C[Searching]
    A --> D[Manipulation]
    A --> E[Permissions]
```

## Basic Navigation
```bash
# List files with details
ls -la

# Show current directory
pwd

# Change directory
cd /path/to/directory
```

## File Operations
```bash
# Copy file
cp source.txt destination/

# Move/rename file
mv oldname.txt newname.txt

# Create file
touch newfile.txt

# Create directory
mkdir new_directory
```

## Content Viewing
```bash
# View entire file
cat filename

# View with pagination
less filename

# View first 10 lines
head filename

# View last 15 lines
tail -n 15 filename
```

## Searching Content
```bash
# Search for pattern in files
grep "pattern" *.log

# Case-insensitive search
grep -i "error" system.log

# Search recursively
grep -r "function_name" src/
```

## Permissions Management
```bash
# Change permissions
chmod 755 script.sh

# Change owner
chown user:group file

# Make executable
chmod +x script.sh
```

## Disk Usage
```bash
# Show disk usage
df -h

# Check directory size
du -sh /path/to/dir

# Find large files
find / -type f -size +100M
```

## Tags
#linux #file-management #reference