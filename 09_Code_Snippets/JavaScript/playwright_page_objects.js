/**
 * Playwright Page Object Model Examples
 * ====================================
 * 
 * This file contains reusable page object classes for Playwright automation.
 * Page objects encapsulate page elements and actions, making tests more maintainable.
 * 
 * Usage:
 *   const loginPage = new LoginPage(page);
 *   await loginPage.login('username', 'password');
 *   await expect(loginPage.successMessage).toBeVisible();
 */

const { expect } = require('@playwright/test');

/**
 * Base Page Object Class
 * Provides common functionality for all page objects
 */
class BasePage {
    constructor(page) {
        this.page = page;
    }

    /**
     * Navigate to a specific URL
     * @param {string} url - The URL to navigate to
     */
    async goto(url) {
        await this.page.goto(url);
    }

    /**
     * Wait for page to load completely
     */
    async waitForPageLoad() {
        await this.page.waitForLoadState('networkidle');
    }

    /**
     * Take a screenshot
     * @param {string} name - Screenshot name
     */
    async takeScreenshot(name) {
        await this.page.screenshot({ path: `screenshots/${name}.png` });
    }

    /**
     * Wait for element to be visible
     * @param {string} selector - Element selector
     * @param {number} timeout - Timeout in milliseconds
     */
    async waitForElement(selector, timeout = 30000) {
        await this.page.waitForSelector(selector, { state: 'visible', timeout });
    }

    /**
     * Scroll element into view
     * @param {string} selector - Element selector
     */
    async scrollToElement(selector) {
        await this.page.locator(selector).scrollIntoViewIfNeeded();
    }

    /**
     * Get page title
     * @returns {Promise<string>} Page title
     */
    async getTitle() {
        return await this.page.title();
    }

    /**
     * Get current URL
     * @returns {Promise<string>} Current URL
     */
    async getCurrentUrl() {
        return this.page.url();
    }
}

/**
 * Login Page Object
 * Handles login functionality
 */
class LoginPage extends BasePage {
    constructor(page) {
        super(page);
        
        // Element selectors
        this.usernameInput = page.locator('[data-testid="username"]');
        this.passwordInput = page.locator('[data-testid="password"]');
        this.loginButton = page.locator('[data-testid="login-button"]');
        this.errorMessage = page.locator('[data-testid="error-message"]');
        this.successMessage = page.locator('[data-testid="success-message"]');
        this.forgotPasswordLink = page.locator('[data-testid="forgot-password"]');
        this.rememberMeCheckbox = page.locator('[data-testid="remember-me"]');
    }

    /**
     * Navigate to login page
     */
    async goto() {
        await super.goto('/login');
        await this.waitForPageLoad();
    }

    /**
     * Perform login action
     * @param {string} username - Username
     * @param {string} password - Password
     * @param {boolean} rememberMe - Whether to check remember me
     */
    async login(username, password, rememberMe = false) {
        await this.usernameInput.fill(username);
        await this.passwordInput.fill(password);
        
        if (rememberMe) {
            await this.rememberMeCheckbox.check();
        }
        
        await this.loginButton.click();
    }

    /**
     * Get error message text
     * @returns {Promise<string>} Error message
     */
    async getErrorMessage() {
        await this.errorMessage.waitFor({ state: 'visible' });
        return await this.errorMessage.textContent();
    }

    /**
     * Check if login form is visible
     * @returns {Promise<boolean>} True if form is visible
     */
    async isLoginFormVisible() {
        return await this.usernameInput.isVisible() && 
               await this.passwordInput.isVisible() && 
               await this.loginButton.isVisible();
    }

    /**
     * Click forgot password link
     */
    async clickForgotPassword() {
        await this.forgotPasswordLink.click();
    }
}

/**
 * Dashboard Page Object
 * Handles dashboard functionality
 */
class DashboardPage extends BasePage {
    constructor(page) {
        super(page);
        
        this.welcomeMessage = page.locator('[data-testid="welcome-message"]');
        this.userMenu = page.locator('[data-testid="user-menu"]');
        this.logoutButton = page.locator('[data-testid="logout-button"]');
        this.navigationMenu = page.locator('[data-testid="nav-menu"]');
        this.searchBox = page.locator('[data-testid="search-box"]');
        this.notificationBell = page.locator('[data-testid="notifications"]');
    }

    /**
     * Navigate to dashboard
     */
    async goto() {
        await super.goto('/dashboard');
        await this.waitForPageLoad();
    }

    /**
     * Get welcome message
     * @returns {Promise<string>} Welcome message text
     */
    async getWelcomeMessage() {
        return await this.welcomeMessage.textContent();
    }

    /**
     * Logout from application
     */
    async logout() {
        await this.userMenu.click();
        await this.logoutButton.click();
    }

    /**
     * Search for content
     * @param {string} query - Search query
     */
    async search(query) {
        await this.searchBox.fill(query);
        await this.searchBox.press('Enter');
    }

    /**
     * Navigate to specific section
     * @param {string} section - Section name
     */
    async navigateToSection(section) {
        await this.navigationMenu.locator(`text=${section}`).click();
    }

    /**
     * Get notification count
     * @returns {Promise<number>} Number of notifications
     */
    async getNotificationCount() {
        const badge = this.notificationBell.locator('.badge');
        if (await badge.isVisible()) {
            return parseInt(await badge.textContent());
        }
        return 0;
    }
}

/**
 * Product List Page Object
 * Handles product listing functionality
 */
class ProductListPage extends BasePage {
    constructor(page) {
        super(page);
        
        this.productGrid = page.locator('[data-testid="product-grid"]');
        this.productCards = page.locator('[data-testid="product-card"]');
        this.filterButton = page.locator('[data-testid="filter-button"]');
        this.sortDropdown = page.locator('[data-testid="sort-dropdown"]');
        this.loadMoreButton = page.locator('[data-testid="load-more"]');
        this.searchInput = page.locator('[data-testid="product-search"]');
    }

    /**
     * Navigate to products page
     */
    async goto() {
        await super.goto('/products');
        await this.waitForPageLoad();
    }

    /**
     * Get all product cards
     * @returns {Promise<Array>} Array of product elements
     */
    async getProductCards() {
        return await this.productCards.all();
    }

    /**
     * Get product count
     * @returns {Promise<number>} Number of products displayed
     */
    async getProductCount() {
        return await this.productCards.count();
    }

    /**
     * Click on specific product
     * @param {number} index - Product index (0-based)
     */
    async clickProduct(index) {
        await this.productCards.nth(index).click();
    }

    /**
     * Search for products
     * @param {string} query - Search query
     */
    async searchProducts(query) {
        await this.searchInput.fill(query);
        await this.searchInput.press('Enter');
        await this.waitForPageLoad();
    }

    /**
     * Apply filter
     * @param {string} filterType - Type of filter
     * @param {string} filterValue - Filter value
     */
    async applyFilter(filterType, filterValue) {
        await this.filterButton.click();
        await this.page.locator(`[data-testid="filter-${filterType}"]`).click();
        await this.page.locator(`[data-testid="filter-option-${filterValue}"]`).click();
        await this.page.locator('[data-testid="apply-filter"]').click();
    }

    /**
     * Sort products
     * @param {string} sortOption - Sort option (price-low, price-high, name, etc.)
     */
    async sortProducts(sortOption) {
        await this.sortDropdown.click();
        await this.page.locator(`[data-testid="sort-${sortOption}"]`).click();
        await this.waitForPageLoad();
    }

    /**
     * Load more products (infinite scroll)
     */
    async loadMoreProducts() {
        if (await this.loadMoreButton.isVisible()) {
            await this.loadMoreButton.click();
            await this.waitForPageLoad();
        }
    }
}

/**
 * Shopping Cart Page Object
 * Handles shopping cart functionality
 */
class ShoppingCartPage extends BasePage {
    constructor(page) {
        super(page);
        
        this.cartItems = page.locator('[data-testid="cart-item"]');
        this.totalPrice = page.locator('[data-testid="total-price"]');
        this.checkoutButton = page.locator('[data-testid="checkout-button"]');
        this.continueShoppingButton = page.locator('[data-testid="continue-shopping"]');
        this.emptyCartMessage = page.locator('[data-testid="empty-cart"]');
    }

    /**
     * Navigate to cart page
     */
    async goto() {
        await super.goto('/cart');
        await this.waitForPageLoad();
    }

    /**
     * Get cart item count
     * @returns {Promise<number>} Number of items in cart
     */
    async getCartItemCount() {
        return await this.cartItems.count();
    }

    /**
     * Get total price
     * @returns {Promise<string>} Total price text
     */
    async getTotalPrice() {
        return await this.totalPrice.textContent();
    }

    /**
     * Remove item from cart
     * @param {number} index - Item index to remove
     */
    async removeItem(index) {
        const removeButton = this.cartItems.nth(index).locator('[data-testid="remove-item"]');
        await removeButton.click();
    }

    /**
     * Update item quantity
     * @param {number} index - Item index
     * @param {number} quantity - New quantity
     */
    async updateQuantity(index, quantity) {
        const quantityInput = this.cartItems.nth(index).locator('[data-testid="quantity-input"]');
        await quantityInput.fill(quantity.toString());
        await quantityInput.press('Enter');
    }

    /**
     * Proceed to checkout
     */
    async proceedToCheckout() {
        await this.checkoutButton.click();
    }

    /**
     * Check if cart is empty
     * @returns {Promise<boolean>} True if cart is empty
     */
    async isCartEmpty() {
        return await this.emptyCartMessage.isVisible();
    }
}

module.exports = {
    BasePage,
    LoginPage,
    DashboardPage,
    ProductListPage,
    ShoppingCartPage
};
