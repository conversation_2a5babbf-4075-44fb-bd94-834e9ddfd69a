"""
Advanced Pytest Fixtures for Testing
=====================================

This module contains reusable pytest fixtures for common testing scenarios
including database setup, API clients, mock services, and test data generation.

Usage:
    Place this file in your conftest.py or import fixtures as needed.
    
Example:
    def test_user_creation(db_session, api_client):
        user = User(name="Test User", email="<EMAIL>")
        db_session.add(user)
        db_session.commit()
        
        response = api_client.get(f"/users/{user.id}")
        assert response.status_code == 200
"""

import pytest
import asyncio
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, patch
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
import requests
import docker
import redis
from faker import Faker


# Database Fixtures
@pytest.fixture(scope="session")
def db_engine():
    """Create a test database engine."""
    engine = create_engine("sqlite:///:memory:", echo=False)
    yield engine
    engine.dispose()


@pytest.fixture(scope="function")
def db_session(db_engine):
    """Create a database session for each test."""
    Session = sessionmaker(bind=db_engine)
    session = Session()
    
    # Create all tables
    from your_app.models import Base
    Base.metadata.create_all(db_engine)
    
    yield session
    
    session.rollback()
    session.close()
    
    # Drop all tables
    Base.metadata.drop_all(db_engine)


# API Client Fixtures
@pytest.fixture
def api_client():
    """HTTP client for API testing."""
    class APIClient:
        def __init__(self, base_url="http://localhost:8000"):
            self.base_url = base_url
            self.session = requests.Session()
            
        def get(self, path, **kwargs):
            return self.session.get(f"{self.base_url}{path}", **kwargs)
            
        def post(self, path, **kwargs):
            return self.session.post(f"{self.base_url}{path}", **kwargs)
            
        def put(self, path, **kwargs):
            return self.session.put(f"{self.base_url}{path}", **kwargs)
            
        def delete(self, path, **kwargs):
            return self.session.delete(f"{self.base_url}{path}", **kwargs)
            
        def set_auth_token(self, token):
            self.session.headers.update({"Authorization": f"Bearer {token}"})
            
    return APIClient()


@pytest.fixture
async def async_api_client():
    """Async HTTP client for API testing."""
    import aiohttp
    
    async with aiohttp.ClientSession() as session:
        class AsyncAPIClient:
            def __init__(self):
                self.session = session
                self.base_url = "http://localhost:8000"
                
            async def get(self, path, **kwargs):
                async with self.session.get(f"{self.base_url}{path}", **kwargs) as response:
                    return response
                    
            async def post(self, path, **kwargs):
                async with self.session.post(f"{self.base_url}{path}", **kwargs) as response:
                    return response
                    
        yield AsyncAPIClient()


# Mock Service Fixtures
@pytest.fixture
def mock_external_service():
    """Mock external service dependencies."""
    with patch('your_app.services.external_api') as mock:
        mock.get_user_data.return_value = {
            "id": 123,
            "name": "Test User",
            "email": "<EMAIL>"
        }
        mock.send_notification.return_value = {"status": "sent"}
        yield mock


@pytest.fixture
def mock_redis():
    """Mock Redis client."""
    mock_redis = Mock()
    mock_redis.get.return_value = None
    mock_redis.set.return_value = True
    mock_redis.delete.return_value = 1
    mock_redis.exists.return_value = False
    
    with patch('your_app.cache.redis_client', mock_redis):
        yield mock_redis


# Container Fixtures
@pytest.fixture(scope="session")
def docker_client():
    """Docker client for container testing."""
    client = docker.from_env()
    yield client
    client.close()


@pytest.fixture(scope="session")
def postgres_container(docker_client):
    """PostgreSQL container for integration testing."""
    container = docker_client.containers.run(
        "postgres:15",
        environment={
            "POSTGRES_DB": "testdb",
            "POSTGRES_USER": "testuser",
            "POSTGRES_PASSWORD": "testpass"
        },
        ports={"5432/tcp": None},
        detach=True,
        remove=True
    )
    
    # Wait for container to be ready
    import time
    time.sleep(5)
    
    yield container
    container.stop()


@pytest.fixture(scope="session")
def redis_container(docker_client):
    """Redis container for caching tests."""
    container = docker_client.containers.run(
        "redis:7-alpine",
        ports={"6379/tcp": None},
        detach=True,
        remove=True
    )
    
    import time
    time.sleep(2)
    
    yield container
    container.stop()


# File System Fixtures
@pytest.fixture
def temp_directory():
    """Temporary directory for file operations."""
    temp_dir = tempfile.mkdtemp()
    yield Path(temp_dir)
    shutil.rmtree(temp_dir)


@pytest.fixture
def sample_files(temp_directory):
    """Create sample files for testing."""
    files = {}
    
    # Create sample CSV file
    csv_file = temp_directory / "sample.csv"
    csv_file.write_text("id,name,email\n1,John,<EMAIL>\n2,Jane,<EMAIL>")
    files['csv'] = csv_file
    
    # Create sample JSON file
    json_file = temp_directory / "sample.json"
    json_file.write_text('{"users": [{"id": 1, "name": "John"}]}')
    files['json'] = json_file
    
    # Create sample text file
    txt_file = temp_directory / "sample.txt"
    txt_file.write_text("Sample text content for testing")
    files['txt'] = txt_file
    
    return files


# Test Data Fixtures
@pytest.fixture
def fake_data():
    """Faker instance for generating test data."""
    return Faker()


@pytest.fixture
def sample_user_data(fake_data):
    """Generate sample user data."""
    return {
        "id": fake_data.random_int(min=1, max=1000),
        "name": fake_data.name(),
        "email": fake_data.email(),
        "phone": fake_data.phone_number(),
        "address": {
            "street": fake_data.street_address(),
            "city": fake_data.city(),
            "country": fake_data.country()
        },
        "created_at": fake_data.date_time_this_year().isoformat()
    }


@pytest.fixture
def sample_product_data(fake_data):
    """Generate sample product data."""
    return {
        "id": fake_data.random_int(min=1, max=1000),
        "name": fake_data.catch_phrase(),
        "description": fake_data.text(max_nb_chars=200),
        "price": float(fake_data.pydecimal(left_digits=3, right_digits=2, positive=True)),
        "category": fake_data.word(),
        "in_stock": fake_data.boolean(),
        "tags": fake_data.words(nb=3)
    }


# Environment Fixtures
@pytest.fixture
def env_vars():
    """Set environment variables for testing."""
    import os
    original_env = os.environ.copy()
    
    test_env = {
        "DATABASE_URL": "sqlite:///:memory:",
        "REDIS_URL": "redis://localhost:6379/0",
        "API_KEY": "test-api-key",
        "DEBUG": "true"
    }
    
    os.environ.update(test_env)
    yield test_env
    
    # Restore original environment
    os.environ.clear()
    os.environ.update(original_env)


# Performance Testing Fixtures
@pytest.fixture
def performance_monitor():
    """Monitor performance metrics during tests."""
    import time
    import psutil
    
    class PerformanceMonitor:
        def __init__(self):
            self.start_time = None
            self.start_memory = None
            
        def start(self):
            self.start_time = time.time()
            self.start_memory = psutil.virtual_memory().used
            
        def stop(self):
            end_time = time.time()
            end_memory = psutil.virtual_memory().used
            
            return {
                "execution_time": end_time - self.start_time,
                "memory_used": end_memory - self.start_memory,
                "cpu_percent": psutil.cpu_percent()
            }
            
    return PerformanceMonitor()


# Async Fixtures
@pytest.fixture
def event_loop():
    """Create an event loop for async tests."""
    loop = asyncio.new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
async def async_db_session():
    """Async database session."""
    from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
    
    engine = create_async_engine("sqlite+aiosqlite:///:memory:")
    async_session = AsyncSession(engine)
    
    yield async_session
    
    await async_session.close()
    await engine.dispose()


# Parametrized Fixtures
@pytest.fixture(params=["chrome", "firefox", "safari"])
def browser_name(request):
    """Parametrized browser fixture for cross-browser testing."""
    return request.param


@pytest.fixture(params=[
    {"width": 1920, "height": 1080},
    {"width": 1366, "height": 768},
    {"width": 375, "height": 667}  # Mobile
])
def screen_resolution(request):
    """Parametrized screen resolution fixture."""
    return request.param
